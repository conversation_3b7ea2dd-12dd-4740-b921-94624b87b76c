import { createFileRoute } from '@tanstack/react-router'
import { HeaderPortal } from '@/services/header/header-portal'
import { Header } from '@/services/header'
import { OrganizationalChart } from '@/features/org-chart'

export const Route = createFileRoute('/_page-layout/org-chart')({
  component: OrgChartPage,
})

function OrgChartPage() {
  return (
    <>
      <HeaderPortal>
        <Header title="Организационная структура" />
      </HeaderPortal>
      <div className="h-[calc(100vh-200px)] w-full">
        <OrganizationalChart />
      </div>
    </>
  )
}
