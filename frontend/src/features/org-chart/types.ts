import type { Node, <PERSON> } from '@xyflow/react'

export interface Employee {
    id: string
    name: string
    position: string
    department: string
    email: string
    phone?: string
    avatar?: string
    managerId?: string
    level: number
}

export interface EmployeeNodeData extends Record<string, unknown> {
    employee: Employee
}

export type EmployeeNode = Node<EmployeeNodeData>

export interface EmployeeEdgeData extends Record<string, unknown> {
    relationship: 'reports-to' | 'manages'
}

export type EmployeeEdge = Edge<EmployeeEdgeData>

export interface OrganizationalData {
    employees: Employee[]
    nodes: EmployeeNode[]
    edges: EmployeeEdge[]
}
