import { Handle, Position } from '@xyflow/react'
import { Mail, Phone } from 'lucide-react'
import type { EmployeeNodeData } from '../types'

interface EmployeeNodeProps {
    data: EmployeeNodeData
}

// Determine card styling based on employee level
function getCardStyling(level: number) {
    switch (level) {
        case 0: {
            // CEO
            return {
                border: 'border-purple-300',
                bg: 'bg-gradient-to-br from-purple-50 to-purple-100',
                accent: 'bg-purple-500',
            }
        }
        case 1: {
            // C-Level
            return {
                border: 'border-blue-300',
                bg: 'bg-gradient-to-br from-blue-50 to-blue-100',
                accent: 'bg-blue-500',
            }
        }
        case 2: {
            // Department Heads
            return {
                border: 'border-green-300',
                bg: 'bg-gradient-to-br from-green-50 to-green-100',
                accent: 'bg-green-500',
            }
        }
        default: {
            // Team Members
            return {
                border: 'border-gray-300',
                bg: 'bg-gradient-to-br from-gray-50 to-gray-100',
                accent: 'bg-gray-500',
            }
        }
    }
}

export function EmployeeNode({ data }: EmployeeNodeProps) {
    const { employee } = data

    const styling = getCardStyling(employee.level)

    return (
        <div
            className={`${styling.bg} ${styling.border} border-2 rounded-xl shadow-lg p-4 min-w-[300px] max-w-[340px] transition-all duration-200 hover:shadow-xl hover:scale-105`}
        >
            <Handle
                type="target"
                position={Position.Top}
                className="w-3 h-3 bg-white border-2 border-gray-300 shadow-sm"
            />

            {/* Accent bar */}
            <div className={`${styling.accent} h-1 rounded-full mb-3 -mx-4 -mt-4`} />

            <div className="flex items-start gap-3">
                <div className="flex-shrink-0">
                    <img
                        src={
                            employee.avatar ||
                            `https://ui-avatars.com/api/?name=${encodeURIComponent(employee.name)}&background=6366f1&color=fff&size=64`
                        }
                        alt={employee.name}
                        className="w-16 h-16 rounded-full object-cover border-3 border-white shadow-md"
                    />
                </div>

                <div className="flex-1 min-w-0">
                    <h3 className="font-bold text-gray-900 text-sm leading-tight mb-1">
                        {employee.name}
                    </h3>
                    <p className="text-xs text-gray-700 font-medium mb-1 leading-tight">
                        {employee.position}
                    </p>
                    <p className="text-xs text-gray-600 mb-3 leading-tight">
                        {employee.department}
                    </p>

                    <div className="space-y-1">
                        <div className="flex items-center gap-1 text-xs text-gray-600">
                            <Mail className="w-3 h-3 flex-shrink-0" />
                            <span className="truncate">{employee.email}</span>
                        </div>
                        {employee.phone && (
                            <div className="flex items-center gap-1 text-xs text-gray-600">
                                <Phone className="w-3 h-3 flex-shrink-0" />
                                <span>{employee.phone}</span>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            <Handle
                type="source"
                position={Position.Bottom}
                className="w-3 h-3 bg-white border-2 border-gray-300 shadow-sm"
            />
        </div>
    )
}
