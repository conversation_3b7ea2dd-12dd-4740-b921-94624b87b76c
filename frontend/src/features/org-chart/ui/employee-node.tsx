import { <PERSON><PERSON>, Position } from '@xyflow/react'
import { Mail, Phone } from 'lucide-react'
import type { EmployeeNodeData } from '../types'

interface EmployeeNodeProps {
    data: EmployeeNodeData
}

// Determine card styling based on employee level - Figma glassmorphism design
function getCardStyling(level: number) {
    switch (level) {
        case 0: {
            // CEO
            return {
                bg: 'bg-[rgba(139,69,19,0.15)]', // <PERSON> tint for CEO
                border: 'border-[rgba(139,69,19,0.3)]',
                accent: 'bg-amber-600',
                textPrimary: 'text-amber-900',
                textSecondary: 'text-amber-700',
            }
        }
        case 1: {
            // C-Level
            return {
                bg: 'bg-[rgba(59,130,246,0.15)]', // Blue tint for C-Level
                border: 'border-[rgba(59,130,246,0.3)]',
                accent: 'bg-blue-600',
                textPrimary: 'text-blue-900',
                textSecondary: 'text-blue-700',
            }
        }
        case 2: {
            // Department Heads
            return {
                bg: 'bg-[rgba(34,197,94,0.15)]', // Green tint for Department Heads
                border: 'border-[rgba(34,197,94,0.3)]',
                accent: 'bg-green-600',
                textPrimary: 'text-green-900',
                textSecondary: 'text-green-700',
            }
        }
        default: {
            // Team Members
            return {
                bg: 'bg-[rgba(62,64,69,0.1)]', // Default gray from Figma
                border: 'border-[rgba(62,64,69,0.2)]',
                accent: 'bg-gray-600',
                textPrimary: 'text-gray-900',
                textSecondary: 'text-gray-700',
            }
        }
    }
}

export function EmployeeNode({ data }: EmployeeNodeProps) {
    const { employee } = data

    const styling = getCardStyling(employee.level)

    return (
        <div
            className={`${styling.bg} ${styling.border} backdrop-blur-[5px] backdrop-filter overflow-clip rounded-[16px] border shadow-lg p-3 min-w-[260px] max-w-[300px] transition-all duration-200 hover:shadow-xl hover:scale-[1.02] relative`}
        >
            <Handle
                type="target"
                position={Position.Top}
                className="w-2 h-2 bg-white/80 border border-gray-300/50 shadow-sm"
            />

            {/* Accent indicator */}
            <div
                className={`${styling.accent} w-2 h-2 rounded-full absolute top-2 right-2 shadow-sm`}
            />

            <div className="flex items-start gap-3">
                <div className="flex-shrink-0">
                    <img
                        src={
                            employee.avatar ||
                            `https://ui-avatars.com/api/?name=${encodeURIComponent(employee.name)}&background=6366f1&color=fff&size=56`
                        }
                        alt={employee.name}
                        className="w-14 h-14 rounded-full object-cover border-2 border-white/60 shadow-md"
                    />
                </div>

                <div className="flex-1 min-w-0">
                    <h3
                        className={`font-semibold ${styling.textPrimary} text-sm leading-tight mb-1`}
                    >
                        {employee.name}
                    </h3>
                    <p
                        className={`text-xs ${styling.textSecondary} font-medium mb-1 leading-tight`}
                    >
                        {employee.position}
                    </p>
                    <p className={`text-xs ${styling.textSecondary} opacity-80 mb-2 leading-tight`}>
                        {employee.department}
                    </p>

                    <div className="space-y-1">
                        <div
                            className={`flex items-center gap-1 text-xs ${styling.textSecondary} opacity-70`}
                        >
                            <Mail className="w-3 h-3 flex-shrink-0" />
                            <span className="truncate">{employee.email}</span>
                        </div>
                        {employee.phone && (
                            <div
                                className={`flex items-center gap-1 text-xs ${styling.textSecondary} opacity-70`}
                            >
                                <Phone className="w-3 h-3 flex-shrink-0" />
                                <span>{employee.phone}</span>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            <Handle
                type="source"
                position={Position.Bottom}
                className="w-2 h-2 bg-white/80 border border-gray-300/50 shadow-sm"
            />
        </div>
    )
}
