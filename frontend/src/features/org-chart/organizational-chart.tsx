import { useCallback, useMemo } from 'react'
import {
    ReactFlow,
    Background,
    BackgroundVariant,
    Controls,
    MiniMap,
    useNodesState,
    useEdgesState,
    addEdge,
    type Connection,
    ConnectionMode,
} from '@xyflow/react'

import { EmployeeNode } from './ui/employee-node'
import { createOrganizationalData } from './data/mock-employees'
import type { EmployeeNodeData } from './types'

const nodeTypes = {
    employee: EmployeeNode,
}

export function OrganizationalChart() {
    const organizationalData = useMemo(() => createOrganizationalData(), [])

    const [
        nodes,
        ,
        onNodesChange,
    ] = useNodesState(organizationalData.nodes)
    const [
        edges,
        setEdges,
        onEdgesChange,
    ] = useEdgesState(organizationalData.edges)

    const onConnect = useCallback(
        (params: Connection) => setEdges((eds) => addEdge(params, eds)),
        [setEdges],
    )

    return (
        <div className="w-full h-full bg-gradient-to-br from-slate-50 to-slate-100 relative">
            <ReactFlow
                nodes={nodes}
                edges={edges}
                onNodesChange={onNodesChange}
                onEdgesChange={onEdgesChange}
                onConnect={onConnect}
                nodeTypes={nodeTypes}
                connectionMode={ConnectionMode.Loose}
                fitView
                fitViewOptions={{
                    padding: 0.3,
                    includeHiddenNodes: false,
                    maxZoom: 1.2,
                }}
                defaultViewport={{ x: 0, y: 0, zoom: 0.7 }}
                minZoom={0.2}
                maxZoom={1.5}
                attributionPosition="bottom-left"
                proOptions={{ hideAttribution: true }}
                nodesDraggable={true}
                nodesConnectable={false}
                elementsSelectable={true}
            >
                <Background
                    variant="dot"
                    gap={25}
                    size={1.5}
                    color="#cbd5e1"
                    style={{ backgroundColor: 'transparent' }}
                />
                <Controls
                    position="top-left"
                    showZoom={true}
                    showFitView={true}
                    showInteractive={false}
                    style={{
                        backgroundColor: 'white',
                        border: '1px solid #e2e8f0',
                        borderRadius: '8px',
                        boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
                    }}
                />
                <MiniMap
                    position="bottom-right"
                    nodeColor={(node) => {
                        const nodeData = node.data as EmployeeNodeData | undefined
                        const level = nodeData?.employee?.level || 0
                        const colors = [
                            '#a855f7',
                            '#3b82f6',
                            '#10b981',
                            '#6b7280',
                        ]
                        return colors[level] || colors[3]
                    }}
                    nodeStrokeWidth={2}
                    pannable
                    zoomable
                    style={{
                        backgroundColor: 'white',
                        border: '1px solid #e2e8f0',
                        borderRadius: '8px',
                        boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
                    }}
                />
            </ReactFlow>

            {/* Legend */}
            <div className="absolute top-4 right-4 bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-xs">
                <h3 className="font-semibold text-gray-900 text-sm mb-3">Уровни организации</h3>
                <div className="space-y-2 text-xs">
                    <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                        <span className="text-gray-700">Руководство (CEO)</span>
                    </div>
                    <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                        <span className="text-gray-700">Директора (C-Level)</span>
                    </div>
                    <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span className="text-gray-700">Руководители отделов</span>
                    </div>
                    <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-gray-500 rounded-full"></div>
                        <span className="text-gray-700">Сотрудники</span>
                    </div>
                </div>
            </div>
        </div>
    )
}
