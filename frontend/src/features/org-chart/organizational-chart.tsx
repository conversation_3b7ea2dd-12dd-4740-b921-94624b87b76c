import { useCallback, useMemo } from 'react'
import {
    ReactFlow,
    Background,
    BackgroundVariant,
    Controls,
    useNodesState,
    useEdgesState,
    addEdge,
    type Connection,
    ConnectionMode,
} from '@xyflow/react'

import { EmployeeNode } from './ui/employee-node'
import { createOrganizationalData } from './data/mock-employees'

const nodeTypes = {
    employee: EmployeeNode,
}

export function OrganizationalChart() {
    const organizationalData = useMemo(() => createOrganizationalData(), [])

    const [
        nodes,
        ,
        onNodesChange,
    ] = useNodesState(organizationalData.nodes)
    const [
        edges,
        setEdges,
        onEdgesChange,
    ] = useEdgesState(organizationalData.edges)

    const onConnect = useCallback(
        (params: Connection) => setEdges((eds) => addEdge(params, eds)),
        [setEdges],
    )

    return (
        <div className="w-full h-full bg-gradient-to-br from-slate-100 via-blue-50 to-indigo-100 relative">
            <ReactFlow
                nodes={nodes}
                edges={edges}
                onNodesChange={onNodesChange}
                onEdgesChange={onEdgesChange}
                onConnect={onConnect}
                nodeTypes={nodeTypes}
                connectionMode={ConnectionMode.Loose}
                fitView
                fitViewOptions={{
                    padding: 0.3,
                    includeHiddenNodes: false,
                    maxZoom: 1.2,
                }}
                defaultViewport={{ x: 0, y: 0, zoom: 0.7 }}
                minZoom={0.2}
                maxZoom={1.5}
                attributionPosition="bottom-left"
                proOptions={{ hideAttribution: true }}
                nodesDraggable={true}
                nodesConnectable={false}
                elementsSelectable={true}
            >
                <Background
                    variant={BackgroundVariant.Dots}
                    gap={30}
                    size={1}
                    color="#e2e8f0"
                    style={{ backgroundColor: 'transparent', opacity: 0.4 }}
                />
                <Controls
                    position="top-left"
                    showZoom={true}
                    showFitView={true}
                    showInteractive={false}
                    style={{
                        backgroundColor: 'white',
                        border: '1px solid #e2e8f0',
                        borderRadius: '8px',
                        boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
                    }}
                />
            </ReactFlow>
        </div>
    )
}
