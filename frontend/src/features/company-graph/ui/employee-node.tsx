import { Handle, Position } from '@xyflow/react'
import { Mail, Phone } from 'lucide-react'
import { memo, useMemo } from 'react'
import type { EmployeeNodeData } from '../types'

interface EmployeeNodeProps {
    data: EmployeeNodeData
}

// Define styling type for better performance and type safety
interface CardStyling {
    bg: string
    border: string
    accent: string
    textPrimary: string
    textSecondary: string
}

// Memoized styling cache to prevent recalculation on every render
const CARD_STYLING_CACHE = new Map<number, CardStyling>()

// Determine card styling based on employee level - Optimized for performance
function getCardStyling(level: number): CardStyling {
    if (CARD_STYLING_CACHE.has(level)) {
        return CARD_STYLING_CACHE.get(level)!
    }

    let styling: CardStyling
    switch (level) {
        case 0: {
            // CEO
            styling = {
                bg: 'bg-white/90', // Simplified for better performance
                border: 'border-amber-200',
                accent: 'bg-amber-500',
                textPrimary: 'text-amber-900',
                textSecondary: 'text-amber-700',
            }
            break
        }
        case 1: {
            // C-Level
            styling = {
                bg: 'bg-white/90', // Simplified for better performance
                border: 'border-blue-200',
                accent: 'bg-blue-500',
                textPrimary: 'text-blue-900',
                textSecondary: 'text-blue-700',
            }
            break
        }
        case 2: {
            // Department Heads
            styling = {
                bg: 'bg-white/90', // Simplified for better performance
                border: 'border-green-200',
                accent: 'bg-green-500',
                textPrimary: 'text-green-900',
                textSecondary: 'text-green-700',
            }
            break
        }
        default: {
            // Team Members
            styling = {
                bg: 'bg-white/90', // Simplified for better performance
                border: 'border-gray-200',
                accent: 'bg-gray-500',
                textPrimary: 'text-gray-900',
                textSecondary: 'text-gray-700',
            }
            break
        }
    }

    // Cache the result for future use
    CARD_STYLING_CACHE.set(level, styling)
    return styling
}

// Memoized component to prevent unnecessary re-renders
const EmployeeNodeComponent = memo(function EmployeeNode({ data }: EmployeeNodeProps) {
    const { employee } = data

    // Memoize styling calculation
    const styling = useMemo(() => getCardStyling(employee.level), [employee.level])

    // Memoize avatar URL to prevent recalculation
    const avatarUrl = useMemo(
        () =>
            employee.avatar ||
            `https://ui-avatars.com/api/?name=${encodeURIComponent(employee.name)}&background=6366f1&color=fff&size=56`,
        [employee.avatar, employee.name],
    )

    return (
        <div
            className={`${styling.bg} ${styling.border} rounded-2xl border shadow-md p-3 min-w-[260px] max-w-[300px] transition-transform duration-150 hover:scale-[1.01] relative will-change-transform`}
        >
            <Handle
                type="target"
                position={Position.Top}
                className="w-2 h-2 bg-white/80 border border-gray-300/50 shadow-sm"
            />

            {/* Accent indicator */}
            <div
                className={`${styling.accent} w-2 h-2 rounded-full absolute top-2 right-2 shadow-sm`}
            />

            <div className="flex items-start gap-3">
                <div className="flex-shrink-0">
                    <img
                        src={avatarUrl}
                        alt={employee.name}
                        className="w-14 h-14 rounded-full object-cover border-2 border-white/50 shadow-sm"
                        loading="lazy"
                    />
                </div>

                <div className="flex-1 min-w-0">
                    <h3
                        className={`font-semibold ${styling.textPrimary} text-sm leading-tight mb-1`}
                    >
                        {employee.name}
                    </h3>
                    <p
                        className={`text-xs ${styling.textSecondary} font-medium mb-1 leading-tight`}
                    >
                        {employee.position}
                    </p>
                    <p className={`text-xs ${styling.textSecondary} opacity-80 mb-2 leading-tight`}>
                        {employee.department}
                    </p>

                    <div className="space-y-1">
                        <div
                            className={`flex items-center gap-1 text-xs ${styling.textSecondary} opacity-70`}
                        >
                            <Mail className="w-3 h-3 flex-shrink-0" />
                            <span className="truncate">{employee.email}</span>
                        </div>
                        {employee.phone && (
                            <div
                                className={`flex items-center gap-1 text-xs ${styling.textSecondary} opacity-70`}
                            >
                                <Phone className="w-3 h-3 flex-shrink-0" />
                                <span>{employee.phone}</span>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            <Handle
                type="source"
                position={Position.Bottom}
                className="w-2 h-2 bg-white/80 border border-gray-300/50 shadow-sm"
            />
        </div>
    )
})

// Export the memoized component
export { EmployeeNodeComponent as EmployeeNode }
