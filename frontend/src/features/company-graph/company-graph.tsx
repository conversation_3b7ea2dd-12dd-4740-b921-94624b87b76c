import { use<PERSON><PERSON>back, useMemo, useEffect } from 'react'
import { stratify, tree } from 'd3-hierarchy'
import {
    ReactFlow,
    ReactFlowProvider,
    Background,
    BackgroundVariant,
    Controls,
    useNodesState,
    useEdgesState,
    useReactFlow,
    addEdge,
    type Connection,
    ConnectionMode,
    Panel,
} from '@xyflow/react'
// import { stratify, tree } from 'd3-hierarchy' // TODO: Install d3-hierarchy

import { EmployeeNode } from './ui/employee-node'
import { initialEdges, initialNodes } from './data/mock-employees'

// Memoize nodeTypes to prevent React Flow re-renders
const nodeTypes = {
    employee: EmployeeNode,
} as const

// TODO: D3 tree layout configuration (when d3-hierarchy is installed)
// const treeLayout = tree()

// Simple hierarchical layout function (will be replaced with D3 when available)

const g = tree()

const getLayoutedElements = (nodes, edges, options) => {
    if (nodes.length === 0) return { nodes, edges }

    const { width, height } = document
        .querySelector(`[data-id="${nodes[0].id}"]`)
        .getBoundingClientRect()
    const hierarchy = stratify()
        .id((node) => node.id)
        .parentId((node) => edges.find((edge) => edge.target === node.id)?.source)
    const root = hierarchy(nodes)
    const layout = g.nodeSize([width * 1, height * 2])(root)

    return {
        nodes: layout
            .descendants()
            .map((node) => ({ ...node.data, position: { x: node.x, y: node.y } })),
        edges,
    }
}

// Internal component that uses React Flow hooks
function CompanyGraphFlow() {
    const { fitView } = useReactFlow()
    const [
        nodes,
        setNodes,
        onNodesChange,
    ] = useNodesState(initialNodes)
    const [
        edges,
        setEdges,
        onEdgesChange,
    ] = useEdgesState(initialEdges)

    const onLayout = useCallback(
        (direction) => {
            const { nodes: layoutedNodes, edges: layoutedEdges } = getLayoutedElements(
                nodes,
                edges,
                {
                    direction,
                },
            )

            setNodes([...layoutedNodes])
            setEdges([...layoutedEdges])

            fitView()
        },
        [nodes, edges],
    )

    // Memoize fitViewOptions to prevent object recreation
    const fitViewOptions = useMemo(
        () => ({
            padding: 0.3,
            includeHiddenNodes: false,
            maxZoom: 1.2,
        }),
        [],
    )

    // Memoize defaultViewport to prevent object recreation
    const defaultViewport = useMemo(
        () => ({
            x: 0,
            y: 0,
            zoom: 0.7,
        }),
        [],
    )

    // Memoize proOptions to prevent object recreation
    const proOptions = useMemo(
        () => ({
            hideAttribution: true,
        }),
        [],
    )

    return (
        <div className="w-full h-full bg-gradient-to-br from-slate-100 via-blue-50 to-indigo-100 relative">
            <ReactFlow
                nodes={nodes}
                edges={edges}
                onNodesChange={onNodesChange}
                onEdgesChange={onEdgesChange}
                nodeTypes={nodeTypes}
                connectionMode={ConnectionMode.Loose}
                fitView
                // fitViewOptions={fitViewOptions}
                // defaultViewport={defaultViewport}
                // minZoom={0.2}
                // maxZoom={1.5}
                proOptions={proOptions}
                // Performance optimizations
                onlyRenderVisibleElements={true}
                nodeOrigin={[0.5, 0.5]}
                deleteKeyCode={null}
                multiSelectionKeyCode={null}
            >
                <Background
                    variant={BackgroundVariant.Dots}
                    gap={40}
                    size={0.8}
                    color="#e2e8f0"
                    style={{ backgroundColor: 'transparent', opacity: 0.3 }}
                />
                {/* <Panel position="top-right">
                    <button onClick={onLayout}>Layout</button>
                </Panel> */}
            </ReactFlow>
        </div>
    )
}

// Main component with ReactFlowProvider wrapper
export function CompanyGraph() {
    return (
        <ReactFlowProvider>
            <CompanyGraphFlow />
        </ReactFlowProvider>
    )
}
