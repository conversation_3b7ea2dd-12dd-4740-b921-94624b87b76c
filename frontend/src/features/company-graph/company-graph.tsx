import { useCallback, useMemo } from 'react'
import {
    ReactFlow,
    Background,
    BackgroundVariant,
    Controls,
    useNodesState,
    useEdgesState,
    addEdge,
    type Connection,
    ConnectionMode,
} from '@xyflow/react'

import { EmployeeNode } from './ui/employee-node'
import { createOrganizationalData } from './data/mock-employees'

// Memoize nodeTypes to prevent React Flow re-renders
const nodeTypes = {
    employee: EmployeeNode,
} as const

export function CompanyGraph() {
    const organizationalData = useMemo(() => createOrganizationalData(), [])

    const [
        nodes,
        ,
        onNodesChange,
    ] = useNodesState(organizationalData.nodes)
    const [
        edges,
        setEdges,
        onEdgesChange,
    ] = useEdgesState(organizationalData.edges)

    const onConnect = useCallback(
        (params: Connection) => setEdges((eds) => addEdge(params, eds)),
        [setEdges],
    )

    // Memoize fitViewOptions to prevent object recreation
    const fitViewOptions = useMemo(
        () => ({
            padding: 0.3,
            includeHiddenNodes: false,
            maxZoom: 1.2,
        }),
        [],
    )

    // Memoize defaultViewport to prevent object recreation
    const defaultViewport = useMemo(
        () => ({
            x: 0,
            y: 0,
            zoom: 0.7,
        }),
        [],
    )

    // Memoize proOptions to prevent object recreation
    const proOptions = useMemo(
        () => ({
            hideAttribution: true,
        }),
        [],
    )

    return (
        <div className="w-full h-full bg-gradient-to-br from-slate-100 via-blue-50 to-indigo-100 relative">
            <ReactFlow
                nodes={nodes}
                edges={edges}
                onNodesChange={onNodesChange}
                onEdgesChange={onEdgesChange}
                onConnect={onConnect}
                nodeTypes={nodeTypes}
                connectionMode={ConnectionMode.Loose}
                fitView
                fitViewOptions={fitViewOptions}
                defaultViewport={defaultViewport}
                minZoom={0.2}
                maxZoom={1.5}
                attributionPosition="bottom-left"
                proOptions={proOptions}
                nodesDraggable={true}
                nodesConnectable={false}
                elementsSelectable={true}
                // Performance optimizations
                onlyRenderVisibleElements={true}
                nodeOrigin={[0.5, 0.5]}
                deleteKeyCode={null}
                multiSelectionKeyCode={null}
            >
                <Background
                    variant={BackgroundVariant.Dots}
                    gap={40}
                    size={0.8}
                    color="#e2e8f0"
                    style={{ backgroundColor: 'transparent', opacity: 0.3 }}
                />
                <Controls
                    position="top-left"
                    showZoom={true}
                    showFitView={true}
                    showInteractive={false}
                    style={{
                        backgroundColor: 'white',
                        border: '1px solid #e2e8f0',
                        borderRadius: '8px',
                        boxShadow: '0 2px 4px -1px rgb(0 0 0 / 0.1)',
                    }}
                />
            </ReactFlow>
        </div>
    )
}
