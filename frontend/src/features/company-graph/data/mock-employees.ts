import type { Employee, EmployeeNode, EmployeeEdge, OrganizationalData } from '../types'

export const mockEmployees: Employee[] = [
    // CEO Level (Level 0)
    {
        id: 'ceo-001',
        name: '<PERSON><PERSON><PERSON> Петрова',
        position: 'Генеральный директор',
        department: 'Руководство',
        email: '<EMAIL>',
        phone: '+7 (495) 123-45-67',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
        level: 0,
    },

    // C-Level (Level 1)
    {
        id: 'cto-001',
        name: 'Д<PERSON><PERSON><PERSON><PERSON><PERSON>',
        position: 'Технический директор',
        department: 'IT',
        email: 'd.i<PERSON><PERSON>@simbios.com',
        phone: '+7 (495) 123-45-68',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        managerId: 'ceo-001',
        level: 1,
    },
    {
        id: 'cfo-001',
        name: 'Елена Смирнова',
        position: 'Финансовый директор',
        department: 'Финансы',
        email: '<EMAIL>',
        phone: '+7 (495) 123-45-69',
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
        managerId: 'ceo-001',
        level: 1,
    },
    {
        id: 'cmo-001',
        name: 'Михаил Козлов',
        position: 'Директор по маркетингу',
        department: 'Маркетинг',
        email: '<EMAIL>',
        phone: '+7 (495) 123-45-70',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
        managerId: 'ceo-001',
        level: 1,
    },

    // Department Heads (Level 2)
    {
        id: 'dev-head-001',
        name: 'Сергей Волков',
        position: 'Руководитель разработки',
        department: 'IT',
        email: '<EMAIL>',
        phone: '+7 (495) 123-45-71',
        avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
        managerId: 'cto-001',
        level: 2,
    },
    {
        id: 'qa-head-001',
        name: 'Ольга Морозова',
        position: 'Руководитель QA',
        department: 'IT',
        email: '<EMAIL>',
        phone: '+7 (495) 123-45-72',
        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
        managerId: 'cto-001',
        level: 2,
    },
    {
        id: 'finance-head-001',
        name: 'Александр Новиков',
        position: 'Главный бухгалтер',
        department: 'Финансы',
        email: '<EMAIL>',
        phone: '+7 (495) 123-45-73',
        avatar: 'https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=150&h=150&fit=crop&crop=face',
        managerId: 'cfo-001',
        level: 2,
    },
    {
        id: 'marketing-head-001',
        name: 'Татьяна Лебедева',
        position: 'Менеджер по продукту',
        department: 'Маркетинг',
        email: '<EMAIL>',
        phone: '+7 (495) 123-45-74',
        avatar: 'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face',
        managerId: 'cmo-001',
        level: 2,
    },

    // Team Members (Level 3)
    {
        id: 'dev-001',
        name: 'Игорь Соколов',
        position: 'Senior Frontend Developer',
        department: 'IT',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face',
        managerId: 'dev-head-001',
        level: 3,
    },
    {
        id: 'dev-002',
        name: 'Мария Федорова',
        position: 'Backend Developer',
        department: 'IT',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
        managerId: 'dev-head-001',
        level: 3,
    },
    {
        id: 'qa-001',
        name: 'Андрей Кузнецов',
        position: 'QA Engineer',
        department: 'IT',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        managerId: 'qa-head-001',
        level: 3,
    },
    {
        id: 'finance-001',
        name: 'Наталья Орлова',
        position: 'Финансовый аналитик',
        department: 'Финансы',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
        managerId: 'finance-head-001',
        level: 3,
    },
    {
        id: 'marketing-001',
        name: 'Владимир Попов',
        position: 'Маркетинг-аналитик',
        department: 'Маркетинг',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
        managerId: 'marketing-head-001',
        level: 3,
    },
]

// Cache for organizational data to prevent recalculation
let cachedOrganizationalData: OrganizationalData | null = null

// Transform employees into React Flow nodes and edges - Optimized
export function createOrganizationalData(): OrganizationalData {
    // Return cached data if available
    if (cachedOrganizationalData) {
        return cachedOrganizationalData
    }

    // Group employees by level for better positioning
    const employeesByLevel: Record<number, Employee[]> = {}
    for (const employee of mockEmployees) {
        if (!employeesByLevel[employee.level]) {
            employeesByLevel[employee.level] = []
        }
        employeesByLevel[employee.level].push(employee)
    }

    const nodes: EmployeeNode[] = mockEmployees.map((employee) => {
        const levelEmployees = employeesByLevel[employee.level]
        const indexInLevel = levelEmployees.findIndex((emp) => emp.id === employee.id)
        const totalInLevel = levelEmployees.length

        // Calculate position for hierarchical layout - Optimized spacing
        const levelWidth = Math.max(totalInLevel * 320, 700)
        const startX = -levelWidth / 2
        const stepX = totalInLevel > 1 ? levelWidth / (totalInLevel - 1) : 0

        return {
            id: employee.id,
            type: 'employee' as const,
            position: {
                x: totalInLevel === 1 ? 0 : startX + indexInLevel * stepX,
                y: employee.level * 180, // Reduced spacing for better performance
            },
            data: { employee },
            // Performance optimizations
            draggable: true,
            selectable: true,
        }
    })

    const edges: EmployeeEdge[] = mockEmployees
        .filter((employee) => employee.managerId)
        .map((employee) => ({
            id: `${employee.managerId}-${employee.id}`,
            source: employee.managerId!,
            target: employee.id,
            type: 'smoothstep' as const,
            data: { relationship: 'manages' as const },
            style: {
                stroke: '#94a3b8',
                strokeWidth: 1.2, // Reduced for better performance
                strokeOpacity: 0.5,
            },
            markerEnd: {
                type: 'arrowclosed' as const,
                color: '#94a3b8',
                width: 10,
                height: 10,
            },
            // Performance optimizations
            animated: false,
            selectable: false,
        }))

    // Cache the result
    cachedOrganizationalData = {
        employees: mockEmployees,
        nodes,
        edges,
    }

    return cachedOrganizationalData
}
