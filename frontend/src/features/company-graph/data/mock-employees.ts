// import { stratify, tree } from 'd3-hierarchy' // TODO: Install d3-hierarchy
import type { Edge } from '@xyflow/react'
import type { Employee } from '../types'

export const mockEmployees: Employee[] = [
    // CEO Level (Level 0)
    {
        id: 'ceo-001',
        name: '<PERSON><PERSON><PERSON>етрова',
        position: 'Генеральный директор',
        department: 'Руководство',
        email: '<EMAIL>',
        phone: '+7 (495) 123-45-67',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    },

    // C-Level (Level 1)
    {
        id: 'cto-001',
        name: 'Д<PERSON><PERSON><PERSON><PERSON><PERSON>',
        position: 'Технический директор',
        department: 'IT',
        email: 'd.<PERSON><PERSON><PERSON>@simbios.com',
        phone: '+7 (495) 123-45-68',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    },
    {
        id: 'cfo-001',
        name: 'Елена Смирнова',
        position: 'Финансовый директор',
        department: 'Финансы',
        email: '<EMAIL>',
        phone: '+7 (495) 123-45-69',
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    },
    {
        id: 'cmo-001',
        name: 'Михаил Козлов',
        position: 'Директор по маркетингу',
        department: 'Маркетинг',
        email: '<EMAIL>',
        phone: '+7 (495) 123-45-70',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    },

    // Department Heads (Level 2)
    {
        id: 'dev-head-001',
        name: 'Сергей Волков',
        position: 'Руководитель разработки',
        department: 'IT',
        email: '<EMAIL>',
        phone: '+7 (495) 123-45-71',
        avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
    },
    {
        id: 'qa-head-001',
        name: 'Ольга Морозова',
        position: 'Руководитель QA',
        department: 'IT',
        email: '<EMAIL>',
        phone: '+7 (495) 123-45-72',
        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
    },
    {
        id: 'finance-head-001',
        name: 'Александр Новиков',
        position: 'Главный бухгалтер',
        department: 'Финансы',
        email: '<EMAIL>',
        phone: '+7 (495) 123-45-73',
        avatar: 'https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=150&h=150&fit=crop&crop=face',
    },
    {
        id: 'marketing-head-001',
        name: 'Татьяна Лебедева',
        position: 'Менеджер по продукту',
        department: 'Маркетинг',
        email: '<EMAIL>',
        phone: '+7 (495) 123-45-74',
        avatar: 'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face',
    },

    // Team Members (Level 3)
    {
        id: 'dev-001',
        name: 'Игорь Соколов',
        position: 'Senior Frontend Developer',
        department: 'IT',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face',
    },
    {
        id: 'dev-002',
        name: 'Мария Федорова',
        position: 'Backend Developer',
        department: 'IT',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    },
    {
        id: 'qa-001',
        name: 'Андрей Кузнецов',
        position: 'QA Engineer',
        department: 'IT',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    },
    {
        id: 'finance-001',
        name: 'Наталья Орлова',
        position: 'Финансовый аналитик',
        department: 'Финансы',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    },
    {
        id: 'marketing-001',
        name: 'Владимир Попов',
        position: 'Маркетинг-аналитик',
        department: 'Маркетинг',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    },
]

export const initialNodes = mockEmployees.map((employee) => {
    return {
        id: employee.id,
        type: 'employee' as const,
        position: { x: 0, y: 0 },
        data: { employee },
    }
})

export const initialEdges: Edge[] = [
    { id: 'ceo-001-cto-001', source: 'ceo-001', target: 'cto-001' },
    { id: 'ceo-001-cfo-001', source: 'ceo-001', target: 'cfo-001' },
    { id: 'ceo-001-cmo-001', source: 'ceo-001', target: 'cmo-001' },
    { id: 'cto-001-dev-head-001', source: 'cto-001', target: 'dev-head-001' },
    { id: 'cto-001-qa-head-001', source: 'cto-001', target: 'qa-head-001' },
    { id: 'cfo-001-finance-head-001', source: 'cfo-001', target: 'finance-head-001' },
    { id: 'cmo-001-marketing-head-001', source: 'cmo-001', target: 'marketing-head-001' },
    { id: 'dev-head-001-dev-001', source: 'dev-head-001', target: 'dev-001' },
    { id: 'dev-head-001-dev-002', source: 'dev-head-001', target: 'dev-002' },
    { id: 'qa-head-001-qa-001', source: 'qa-head-001', target: 'qa-001' },
    { id: 'finance-head-001-finance-001', source: 'finance-head-001', target: 'finance-001' },
    {
        id: 'marketing-head-001-marketing-001',
        source: 'marketing-head-001',
        target: 'marketing-001',
    },
]

// Cache for organizational data to prevent recalculation
// let cachedOrganizationalData: OrganizationalData | null = null

// // Layout is now handled in the component

// // Transform employees into React Flow nodes and edges - With D3 Layout
// export function createOrganizationalData(): OrganizationalData {
//     // Return cached data if available
//     if (cachedOrganizationalData) {
//         return cachedOrganizationalData
//     }

//     // Create initial nodes without positioning
//     const nodes: EmployeeNode[] = mockEmployees.map((employee) => ({
//         id: employee.id,
//         type: 'employee' as const,
//         position: { x: 0, y: 0 }, // Will be updated by D3 layout
//         data: { employee },
//         // Performance optimizations
//         draggable: true,
//         selectable: true,
//     }))

//     const edges: EmployeeEdge[] = mockEmployees
//         .filter((employee) => employee.managerId)
//         .map((employee) => ({
//             id: `${employee.managerId}-${employee.id}`,
//             source: employee.managerId!,
//             target: employee.id,
//             type: 'smoothstep' as const,
//             data: { relationship: 'manages' as const },
//             style: {
//                 stroke: '#94a3b8',
//                 strokeWidth: 1.2, // Reduced for better performance
//                 strokeOpacity: 0.5,
//             },
//             markerEnd: {
//                 type: 'arrowclosed' as const,
//                 color: '#94a3b8',
//                 width: 10,
//                 height: 10,
//             },
//             // Performance optimizations
//             animated: false,
//             selectable: false,
//         }))

//     // Cache the result (layout will be applied in component)
//     cachedOrganizationalData = {
//         employees: mockEmployees,
//         nodes,
//         edges,
//     }

//     return cachedOrganizationalData
// }
